<?php
/**
 * Test script to verify category matching for Excel import
 */

// Include CakePHP bootstrap
require_once 'config/bootstrap.php';

use Cake\ORM\TableRegistry;
use Cake\Datasource\ConnectionManager;

try {
    echo "<h2>🧪 Testing Category Matching for Excel Import</h2>";
    
    // Get Categories table
    $categoriesTable = TableRegistry::getTableLocator()->get('Categories');
    
    echo "<h3>1. Available Categories in Database</h3>";
    
    // Get all active categories
    $allCategories = $categoriesTable->find()
        ->where(['status' => 'A'])
        ->order(['name' => 'ASC'])
        ->all();
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Name</th><th>Parent ID</th><th>Status</th></tr>";
    
    foreach ($allCategories as $category) {
        echo "<tr>";
        echo "<td>" . $category->id . "</td>";
        echo "<td>" . htmlspecialchars($category->name) . "</td>";
        echo "<td>" . ($category->parent_id ?? 'NULL') . "</td>";
        echo "<td>" . $category->status . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>2. Testing Category Matching Methods</h3>";
    
    // Test some sample category names
    $testCategories = ['Electronics', 'Home & Garden', 'Fashion', 'Sports', 'Beauty'];
    
    foreach ($testCategories as $testCategory) {
        echo "<h4>Testing: '{$testCategory}'</h4>";
        
        // Test exact match
        $exactMatch = $categoriesTable->find()
            ->where(['name' => $testCategory, 'status' => 'A'])
            ->first();
        
        echo "Exact match: " . ($exactMatch ? "✅ Found (ID: {$exactMatch->id})" : "❌ Not found") . "<br>";
        
        // Test fuzzy match
        $fuzzyMatch = $categoriesTable->find()
            ->where(['name LIKE' => '%' . trim($testCategory) . '%', 'status' => 'A'])
            ->first();
        
        echo "Fuzzy match: " . ($fuzzyMatch ? "✅ Found (ID: {$fuzzyMatch->id}, Name: '{$fuzzyMatch->name}')" : "❌ Not found") . "<br>";
        echo "<br>";
    }
    
    echo "<h3>3. Testing Parent Categories (for Excel dropdown)</h3>";
    
    $parentCategories = $categoriesTable->find()
        ->where(['status' => 'A', 'parent_id IS' => null])
        ->order(['name' => 'ASC'])
        ->all();
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Name</th><th>Child Count</th></tr>";
    
    foreach ($parentCategories as $parentCategory) {
        $childCount = $categoriesTable->find()
            ->where(['parent_id' => $parentCategory->id, 'status' => 'A'])
            ->count();
        
        echo "<tr>";
        echo "<td>" . $parentCategory->id . "</td>";
        echo "<td>" . htmlspecialchars($parentCategory->name) . "</td>";
        echo "<td>" . $childCount . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>4. Sample Excel Data Format</h3>";
    echo "<p>When creating Excel files, use these exact category names:</p>";
    echo "<ul>";
    foreach ($parentCategories as $parentCategory) {
        echo "<li>" . htmlspecialchars($parentCategory->name) . "</li>";
    }
    echo "</ul>";
    
    echo "<h2>✅ Category Testing Complete</h2>";
    echo "<p><strong>Recommendation:</strong> Use the exact category names listed above in your Excel file for best results.</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Test Failed</h2>";
    echo "Error: " . $e->getMessage() . "<br>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
}

echo "<br><hr>";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>";
?>
