<?php
// Test script to verify country-specific brand and category validation
// This simulates the import validation logic

// Test data - simulate different brands/categories with same name but different countries
$testData = [
    // Simulate brands table data
    'brands' => [
        ['id' => 1, 'name' => 'Samsung', 'status' => 'A', 'country_id' => 1], // UAE
        ['id' => 2, 'name' => 'Samsung', 'status' => 'A', 'country_id' => 2], // Kuwait
        ['id' => 3, 'name' => 'Samsung', 'status' => 'A', 'country_id' => null], // Global
        ['id' => 4, 'name' => 'LG', 'status' => 'A', 'country_id' => 1], // UAE only
        ['id' => 5, 'name' => 'Sony', 'status' => 'A', 'country_id' => null], // Global only
    ],
    
    // Simulate categories table data  
    'categories' => [
        ['id' => 1, 'name' => 'Electronics', 'status' => 'A', 'country_id' => 1], // UAE
        ['id' => 2, 'name' => 'Electronics', 'status' => 'A', 'country_id' => 2], // Kuwait  
        ['id' => 3, 'name' => 'Electronics', 'status' => 'A', 'country_id' => null], // Global
        ['id' => 4, 'name' => 'Home & Garden', 'status' => 'A', 'country_id' => 1], // UAE only
        ['id' => 5, 'name' => 'Fashion', 'status' => 'A', 'country_id' => null], // Global only
    ]
];

// Test function to simulate the improved brand validation
function validateBrand($brandName, $selectedCountryId, $brands) {
    echo "Testing brand validation for: '$brandName' with country_id: $selectedCountryId\n";
    
    foreach ($brands as $brand) {
        $nameMatches = stripos($brand['name'], $brandName) !== false;
        $statusActive = $brand['status'] === 'A';
        $countryMatches = ($brand['country_id'] === $selectedCountryId) || ($brand['country_id'] === null);
        
        if ($nameMatches && $statusActive && $countryMatches) {
            echo "✓ Found brand: {$brand['name']} (ID: {$brand['id']}, Country: " . ($brand['country_id'] ?? 'Global') . ")\n";
            return ['success' => true, 'brand_id' => $brand['id']];
        }
    }
    
    echo "✗ Brand '$brandName' not found for country $selectedCountryId\n";
    return ['success' => false, 'message' => "Brand '$brandName' not found or is inactive for the selected country."];
}

// Test function to simulate the improved category validation
function validateCategory($categoryName, $selectedCountryId, $categories) {
    echo "Testing category validation for: '$categoryName' with country_id: $selectedCountryId\n";
    
    foreach ($categories as $category) {
        $nameMatches = stripos($category['name'], $categoryName) !== false;
        $statusActive = $category['status'] === 'A';
        $countryMatches = ($category['country_id'] === $selectedCountryId) || ($category['country_id'] === null);
        
        if ($nameMatches && $statusActive && $countryMatches) {
            echo "✓ Found category: {$category['name']} (ID: {$category['id']}, Country: " . ($category['country_id'] ?? 'Global') . ")\n";
            return ['success' => true, 'category_id' => $category['id']];
        }
    }
    
    echo "✗ Category '$categoryName' not found for country $selectedCountryId\n";
    return ['success' => false, 'message' => "Category '$categoryName' not found for the selected country"];
}

echo "=== Testing Country-Specific Brand and Category Validation ===\n\n";

// Test Case 1: UAE country selected (country_id = 1)
echo "--- Test Case 1: UAE Selected (country_id = 1) ---\n";
$result1 = validateBrand('Samsung', 1, $testData['brands']);
$result2 = validateCategory('Electronics', 1, $testData['categories']);
echo "\n";

// Test Case 2: Kuwait country selected (country_id = 2)
echo "--- Test Case 2: Kuwait Selected (country_id = 2) ---\n";
$result3 = validateBrand('Samsung', 2, $testData['brands']);
$result4 = validateCategory('Electronics', 2, $testData['categories']);
echo "\n";

// Test Case 3: UAE with UAE-only brand
echo "--- Test Case 3: UAE with UAE-only Brand ---\n";
$result5 = validateBrand('LG', 1, $testData['brands']);
$result6 = validateCategory('Home & Garden', 1, $testData['categories']);
echo "\n";

// Test Case 4: Kuwait trying to access UAE-only brand (should fail)
echo "--- Test Case 4: Kuwait trying UAE-only Brand (should fail) ---\n";
$result7 = validateBrand('LG', 2, $testData['brands']);
$result8 = validateCategory('Home & Garden', 2, $testData['categories']);
echo "\n";

// Test Case 5: Global brand/category (should work for any country)
echo "--- Test Case 5: Global Brand/Category (should work for any country) ---\n";
$result9 = validateBrand('Sony', 1, $testData['brands']);
$result10 = validateCategory('Fashion', 2, $testData['categories']);
echo "\n";

// Test Case 6: Non-existent brand/category
echo "--- Test Case 6: Non-existent Brand/Category ---\n";
$result11 = validateBrand('NonExistent', 1, $testData['brands']);
$result12 = validateCategory('NonExistent', 1, $testData['categories']);
echo "\n";

echo "=== Test Results Summary ===\n";
echo "Before Fix: Brand/Category validation would pick the first match regardless of country.\n";
echo "After Fix: Brand/Category validation now considers country_id OR global entries (country_id IS null).\n";
echo "This ensures the correct brand/category is selected based on the selected country during import.\n";
