function selectDropdownItem(element, languageCode, country) {

    localStorage.setItem('user_country', country);
    localStorage.setItem('user_language', languageCode);
    $.ajax({
        url: '/Home/getSiteSettings',
        method: 'POST',
        contentType: 'application/json',
        headers: {
            'X-CSRF-Token': $('meta[name="csrfToken"]').attr('content')
        },
        data: JSON.stringify({
            country: country,
            lang: languageCode.toLowerCase()
        }),
        beforeSend: function(xhr) {
            // Include CSRF token in the request headers
            xhr.setRequestHeader('X-CSRF-Token', $('meta[name="csrfToken"]').attr('content'));
        },
        success: function (data) {
            window.location.reload();
        },
        error: function (xhr, status, error) {
            console.error('Error:', error);
            if (xhr.status === 403) {
                alert('CSRF token validation failed. Please refresh the page and try again.');
            }
        }
    });
}

// function selectDropdownregion(element) {
//     const button = document.getElementById("dropdownregion");
//     const selectedContent = element.innerHTML;
//     button.innerHTML = selectedContent;
// }

// document.addEventListener('DOMContentLoaded', function() {
// fetch('https://ipapi.co/json/')
// .then(response => response.json())
// .then(data => {
//     const country = data.country_name;
//     if (country === 'Qatar' || country === 'Saudi Arabia') {
//         selectDropdownItem('', 'eng', country);
//     } else {
//         selectDropdownItem('', 'eng', 'Qatar');
//     }
// });
// });

document.addEventListener('DOMContentLoaded', function () {
  // Check if country is already stored in sessionStorage
  const countryInSession = sessionStorage.getItem('user_country');

  if (!countryInSession) {
    // Only fetch if not stored yet
    fetch('https://ipapi.co/json/')
      .then(response => response.json())
      .then(data => {
        const country = data.country_name?.trim() || 'Qatar';
        console.log('Fetched country:', country);
        // Save only to sessionStorage (removes after browser close)
        sessionStorage.setItem('user_country', country);

        // Call your dropdown function
        if (country === 'Qatar' || country === 'Saudi Arabia') {
          selectDropdownItem('', 'eng', country);
        } else {
          selectDropdownItem('', 'eng', 'Qatar');
        }
      })
      .catch(() => {
         console.log('Fetched country 2:');
        // On error, fallback and store default in sessionStorage
        sessionStorage.setItem('user_country', 'Qatar');
        selectDropdownItem('', 'eng', 'Qatar');
      });
  }
});

function customNumberFormat(price) {
  return parseFloat(price).toFixed(2);
}
function customNumberFormatWithCountry(price) {
  const formattedPrice = parseFloat(price).toFixed(0);
  let country = localStorage.getItem('user_country') || 'Qatar';
 
  switch (country) {
    case 'Qatar':
      return `${formattedPrice} QAR`;
    case 'Saudi Arabia':
      return `${formattedPrice} SAR`;
    default:
      return `${formattedPrice} QAR`;
  }
}